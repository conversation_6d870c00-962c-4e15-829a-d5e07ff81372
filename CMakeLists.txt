cmake_minimum_required(VERSION 4.0.3)
project(MiWebServer VERSION 0.1.0 LANGUAGES C CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

add_subdirectory(Oat++)
add_subdirectory(Extensions/Oatpp-Sqlite)

# Component build options

add_executable(MiWebServer
    main.cpp
)
# Find all cpp files recursively
file(GLOB_RECURSE CPP_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/components/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/Service/*.cpp"
)

# Add sources to executable
target_sources(MiWebServer PRIVATE ${CPP_SOURCES})
# Include directories
target_include_directories(MiWebServer PRIVATE
    CLibComponent
    Controller
    Service
    Dto
    Extensions/Oatpp-Sqlite
    Extensions/Oatpp-Sqlite/sqlite  # Make sqlite3.h available globally
)

# Link to Oat++ and oatpp-sqlite
target_link_libraries(MiWebServer PRIVATE
    oatpp
    oatpp-sqlite
    sqlite3-headers
)