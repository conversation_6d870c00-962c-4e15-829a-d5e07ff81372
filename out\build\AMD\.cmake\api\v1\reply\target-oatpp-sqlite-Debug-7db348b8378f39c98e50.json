{"archive": {}, "artifacts": [{"path": "Extensions/Oatpp-Sqlite/liboatpp-sqlite.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_include_directories", "set_target_properties", "target_sources"], "files": ["Extensions/Oatpp-Sqlite/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}, {"command": 1, "file": 0, "line": 72, "parent": 0}, {"command": 2, "file": 0, "line": 64, "parent": 0}, {"command": 3, "file": 0, "line": 57, "parent": 0}, {"command": 4, "file": 0, "line": 44, "parent": 0}, {"command": 4, "file": 0, "line": 53, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=c++17"}], "includes": [{"backtrace": 3, "path": "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite"}, {"backtrace": 3, "path": "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/sqlite"}, {"backtrace": 2, "path": "D:/MiWebApp/WebServerApp/Oat++"}], "language": "CXX", "languageStandard": {"backtraces": [4], "standard": "17"}, "sourceIndexes": [0, 2, 4, 6, 9, 12, 14, 16, 18, 20, 22]}], "dependencies": [{"backtrace": 2, "id": "oatpp::@103db6bc44d64c5e8dd4"}, {"backtrace": 2, "id": "sqlite::@a6bb91110ae3f8dbd25e"}], "id": "oatpp-sqlite::@a6bb91110ae3f8dbd25e", "name": "oatpp-sqlite", "nameOnDisk": "liboatpp-sqlite.a", "paths": {"build": "Extensions/Oatpp-Sqlite", "source": "Extensions/Oatpp-Sqlite"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 6, 9, 12, 14, 16, 18, 20, 22]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5, 7, 8, 10, 11, 13, 15, 17, 19, 21, 23]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/Connection.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/Connection.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/ConnectionProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/ConnectionProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/Executor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/Executor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/QueryResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/QueryResult.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/Types.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/Utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/Utils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/orm.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/Deserializer.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/Deserializer.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/ResultMapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/ResultMapper.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/Serializer.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/Serializer.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/type/Blob.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/type/Blob.hpp", "sourceGroupIndex": 1}, {"backtrace": 6, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/ql_template/Parser.cpp", "sourceGroupIndex": 0}, {"backtrace": 6, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/ql_template/Parser.hpp", "sourceGroupIndex": 1}, {"backtrace": 6, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/ql_template/TemplateValueProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 6, "path": "Extensions/Oatpp-Sqlite/oatpp-sqlite/ql_template/TemplateValueProvider.hpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}