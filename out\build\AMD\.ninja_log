# ninja log v7
39	407	7724446871357042	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj	a6631bcd383c220b
131	558	7724446872272333	Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj	dd9d9cd60b2e6869
138	571	7724446872345551	Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj	5da683dd99b52fef
160	592	7724446872565946	Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj	54b57418cf946cd1
174	654	7724446872707890	Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj	765a13ed7743ce38
9	767	7724446871052092	Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj	d5f921724202bf4f
207	731	7724446873034133	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj	4ca80235b7cc800e
27	794	7724446871232221	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj	7e0f5f29fea8424
18	920	7724446871144596	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj	7672900758c54b7
72	852	7724446871688528	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj	a31df5a619c5ab4b
81	834	7724446871776244	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj	350f3b162bc348de
51	939	7724446871476133	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj	f3be4aeec41eef32
299	877	7724446873949955	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj	12e963b6592c18bd
121	995	7724446872167834	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj	2a22d10db4e9b9bb
325	957	7724446874214030	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj	f267895a7bc6333d
67	970	7724446871631636	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj	ad25555d350ba67f
88	1015	7724446871839018	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj	1d6ab0a7cf486610
271	1059	7724446873674743	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj	57df52527929b09f
31	1036	7724446871273483	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj	4e724f15a584f9c4
408	1124	7724446875041654	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj	a5fb4115a3813e9e
282	1086	7724446873786865	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj	a2c569eb3083fbe4
233	1161	7724446873288163	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj	560fce0e2a8d8289
193	1202	7724446872891321	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj	cf9caadd1caf326a
222	1220	7724446873187354	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj	4ad7cdb4683ed1e9
558	1297	7724446876541690	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj	c211c62d4b6fe81f
99	1275	7724446871959470	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj	6f17f6bc3f75cad8
185	1257	7724446872817889	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj	1f3d3f09ad8c47c
571	1237	7724446876676135	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj	6fe6d242d03b6ba8
357	1184	7724446874534871	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj	a1fd9cee5f5ab369
57	1338	7724446871533289	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj	792bd728bf995b19
106	1317	7724446872027428	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj	1c7cbbceedd9d695
309	1390	7724446874053386	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj	5d5027671ee3aca8
995	1482	7724446880912116	Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj	9e85c1c0f6f3bd8f
337	1471	7724446874333040	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj	2c44553129b08cb
767	1569	7724446878634819	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj	d50f8a03aa06193e
1060	1581	7724446881560591	Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj	a3e2e57267ce9786
44	1736	7724446871398501	Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj	7a9585b3e7ddf8fd
592	1717	7724446876883148	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj	231778157f603f80
249	1798	7724446873452274	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj	b8fe1701b02f590e
14	1822	7724446871098227	Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj	fd86315d93b72145
970	1887	7724446880663510	Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj	3a36fd43689f11fb
877	1914	7724446879731059	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj	f80c6a285aeaae37
939	1978	7724446880352596	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj	414d235b5f44abae
654	1953	7724446877508443	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj	11630bdbe81f3647
794	1898	7724446878903392	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj	f58a7f4294fda7a3
834	2014	7724446879302745	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj	b20e36cfdb408fc3
1015	2032	7724446881114839	Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj	b82a858f6e15cd45
853	2103	7724446879488697	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj	728e6f51164102ae
957	2060	7724446880534391	Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj	36786da85384c296
1086	2146	7724446881824987	Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj	5c2b36d1f4b63b15
1202	2079	7724446882985688	Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj	a2243bfda358accd
731	2165	7724446878272930	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj	15353b1b14537456
1036	2125	7724446881327578	Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj	c8c25d7ec8cb0490
1184	2184	7724446882803996	Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj	46266695d45060c5
1237	2208	7724446883333311	Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj	363133b5dcf9f25b
920	2228	7724446880166329	Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj	d494cf7f0148119
1220	2326	7724446883161898	Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj	c5f45147d67675a0
1318	2260	7724446884141155	Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj	42e0b53fafd40c2f
1339	2287	7724446884353141	Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj	f64c59aa16472238
152	2426	7724446872476256	Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj	858cff15c723fa2
1297	2549	7724446883939565	Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj	6a54e761d89b8c88
1257	2409	7724446883529842	Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj	bc449ad297c3f740
1275	2495	7724446883716215	Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj	30a3298ce3139518
1161	2847	7724446882570336	Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj	2ce824ec4a54efae
1914	2906	7724446890108102	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj	e9a04020749d8098
1736	2922	7724446888324314	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj	4f4c0a2d3645896d
1717	3020	7724446888132491	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj	7c2010a28824a800
2014	2950	7724446891104372	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj	31fed0c86b013edf
1887	3000	7724446889827719	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj	7695b7690bb53fb
1898	3091	7724446889941386	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj	112b1e3792b2fd24
1979	3106	7724446890750410	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj	dd5c6fd2f0d2b543
1125	3224	7724446882210708	Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj	92c90b89d85a2bef
2185	3153	7724446892813898	Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj	c250bf863f2632ce
2033	3136	7724446891285582	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj	9bad1d12e56aa043
1953	3177	7724446890496758	Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj	42293fdfd5dac6f1
1581	3206	7724446886776874	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj	9033a8acc6386bcb
1822	3242	7724446889185852	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj	d185f778a6953f9f
2079	3295	7724446891752681	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj	3a3c6806fe135979
1798	3340	7724446888947909	Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj	60c22710d95bdd45
2103	3452	7724446891995389	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj	86f509cb80dc118b
2125	3543	7724446892213751	Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj	d51ae8d485f04fff
2229	3589	7724446893254436	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj	b67b4dd33587bcc9
1472	3740	7724446885679957	Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj	eed456332973fa3
2165	3712	7724446892612110	Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj	897c2e79ae4cf78
2208	3727	7724446893041383	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj	eb9d603a8073d27c
2409	3654	7724446895053114	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj	e224f0d064e55704
2495	3783	7724446895915444	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj	fd7362daeb898846
2287	3634	7724446893833479	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj	df33ea1d4a7f8698
1390	3608	7724446884868231	Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj	a8c3a1db054041ef
2260	3690	7724446893564612	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj	d639a9de108660bb
2326	3673	7724446894221293	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj	645d70027c000ca0
2848	3855	7724446899438460	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj	1968b7168309aaaf
2550	4187	7724446896457899	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj	50a916335d4d41f
2922	4116	7724446900181791	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj	90099414facf7754
2950	4186	7724446900460792	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj	524f554e09a68d41
3177	4187	7724446902733444	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj	7acc8fefc4d7daec
2426	4154	7724446895223794	Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj	8469ff10adfb23c8
2906	4318	7724446900021716	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj	4524eb825b0530a3
1482	4098	7724446885783683	Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj	8177790848203f5
3000	4276	7724446900963462	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj	c49d1221fab5c1a3
3091	4262	7724446901875829	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj	ac7a7157827f0ea8
3137	4265	7724446902331418	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj	1d2afeb8ebf7277a
1569	4314	7724446886652593	Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj	bcbcfde581dc0a25
3206	4442	7724446903026512	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj	3bdab1c1a691056b
3340	4420	7724446904358887	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj	415b43c954a892f3
3452	4453	7724446905485468	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj	839ea3bd72e4678d
2146	4471	7724446892425960	Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj	1fcd23b833b9dbe1
3225	4398	7724446903206477	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj	4ffaa66fd20d2179
3107	4537	7724446902034827	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj	8d16c1cca17f9a6b
2061	4493	7724446891565998	Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj	c9539fdd203fdf08
3153	4580	7724446902495843	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj	c5c23736b3ce9505
3020	4638	7724446901167507	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj	13cc84af52eb86ba
3243	4736	7724446903392740	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj	477d4373faa6113b
3544	4836	7724446906400335	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj	a9847f32335587e6
3727	4810	7724446908231067	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj	3262c36733127c4a
3740	4800	7724446908363206	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj	ff9efcd300bc1147
3295	4851	7724446903914022	Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj	24533832719f1fa1
3783	4936	7724446908791597	Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj	519b5be12af3ee5a
3712	4970	7724446908083157	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj	c803120b92bed2b5
3673	5108	7724446907690188	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj	58d18c44e2affa34
3690	5173	7724446907864805	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj	66b14a94618f64bc
3654	5152	7724446907505124	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj	e64222bb0a52ec7c
3609	5320	7724446907051167	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj	4bc2ebbca9c70677
3589	5478	7724446906855124	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj	71cfa191abaaaa3e
3635	5644	7724446907310087	Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj	c9cd2498bb524f78
7	1523	7724533640821839	CMakeFiles/MiWebServer.dir/main.cpp.obj	56219afc1717f4c
5644	9339	7724446927396397	Oat++/liboatpp.a	e915e60ae2ff0bd3
9339	12596	7724446964358247	MiWebServer.exe	dabdf364ade205ee
4116	4717	7724446912127449	CMakeFiles/MiWebServer.dir/components/Microsystem/Microsystem.cpp.obj	ea93e9e11313f981
83	3486	7724431711540802	Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/sqlite/sqlite3.c.obj	d99cd8aee879f44a
4098	5024	7724446911942119	CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj	41394393db568735
4154	8075	7724446912507370	CMakeFiles/MiWebServer.dir/Oatpp-Sqlite/sqlite/sqlite3.c.obj	6fabaeca02f2a20f
4	43	7724446840751578	clean	6c8708331739acdd
92	618	7724540335030476	CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj	223140303f859b1a
83	1716	7724540334942597	CMakeFiles/MiWebServer.dir/main.cpp.obj	a388225d63d0883c
7	2841	7724540334181257	Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj	f227da0a7f48d630
8	101	7724541133347421	Extensions/Oatpp-Sqlite/libsqlite.a	437612220a09d317
20	666	7724541133471479	Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj	7a821b18e0d2aea4
7	604	7724542493503477	Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj	3f36f4801dfe75c1
56	578	7724542654237097	Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj	d0d38c22be4794f5
45	705	7724551131740626	Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj	2dd1f6df05154340
47	668	7724552934783105	Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj	11f361c9b380af1e
16	642	7724556044939741	Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj	3ec0a0c68ff55c6a
12	716	7724556044898455	Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj	2a6e9cc765e676dc
8	1325	7724556358749114	Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj	9bc21700801ac688
