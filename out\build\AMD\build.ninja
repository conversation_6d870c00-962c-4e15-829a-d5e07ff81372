# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: MiWebServer
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/MiWebApp/WebServerApp/out/build/AMD/
# =============================================================================
# Object build statements for EXECUTABLE target MiWebServer


#############################################
# Order-only phony target for MiWebServer

build cmake_object_order_depends_target_MiWebServer: phony || cmake_object_order_depends_target_oatpp

build CMakeFiles/MiWebServer.dir/main.cpp.obj: CXX_COMPILER__MiWebServer_unscanned_Debug D$:/MiWebApp/WebServerApp/main.cpp || cmake_object_order_depends_target_MiWebServer
  CONFIG = Debug
  DEP_FILE = CMakeFiles\MiWebServer.dir\main.cpp.obj.d
  FLAGS = -g -std=gnu++17
  INCLUDES = -ID:/MiWebApp/WebServerApp/CLibComponent -ID:/MiWebApp/WebServerApp/Controller -ID:/MiWebApp/WebServerApp/Service -ID:/MiWebApp/WebServerApp/Dto -ID:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = CMakeFiles\MiWebServer.dir
  OBJECT_FILE_DIR = CMakeFiles\MiWebServer.dir

build CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj: CXX_COMPILER__MiWebServer_unscanned_Debug D$:/MiWebApp/WebServerApp/Service/DatabaseService.cpp || cmake_object_order_depends_target_MiWebServer
  CONFIG = Debug
  DEP_FILE = CMakeFiles\MiWebServer.dir\Service\DatabaseService.cpp.obj.d
  FLAGS = -g -std=gnu++17
  INCLUDES = -ID:/MiWebApp/WebServerApp/CLibComponent -ID:/MiWebApp/WebServerApp/Controller -ID:/MiWebApp/WebServerApp/Service -ID:/MiWebApp/WebServerApp/Dto -ID:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = CMakeFiles\MiWebServer.dir
  OBJECT_FILE_DIR = CMakeFiles\MiWebServer.dir\Service


# =============================================================================
# Link build statements for EXECUTABLE target MiWebServer


#############################################
# Link the executable MiWebServer.exe

build MiWebServer.exe: CXX_EXECUTABLE_LINKER__MiWebServer_Debug CMakeFiles/MiWebServer.dir/main.cpp.obj CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj | Oat++/liboatpp.a || Oat++/liboatpp.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = Oat++/liboatpp.a  -loatpp-sqlite  -lwsock32  -lws2_32  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\MiWebServer.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = MiWebServer.exe
  TARGET_IMPLIB = libMiWebServer.dll.a
  TARGET_PDB = MiWebServer.exe.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\MiWebApp\WebServerApp\out\build\AMD && "C:\Program Files\CMake\bin\cmake-gui.exe" -SD:\MiWebApp\WebServerApp -BD:\MiWebApp\WebServerApp\out\build\AMD"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\MiWebApp\WebServerApp\out\build\AMD && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SD:\MiWebApp\WebServerApp -BD:\MiWebApp\WebServerApp\out\build\AMD"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/MiWebApp/WebServerApp/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target oatpp


#############################################
# Order-only phony target for oatpp

build cmake_object_order_depends_target_oatpp: phony || .

build Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/algorithm/CRC.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\algorithm\CRC.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\algorithm

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/IODefinitions.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\IODefinitions.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/ConditionVariable.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\ConditionVariable.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Coroutine.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\Coroutine.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/CoroutineWaitList.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\CoroutineWaitList.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Error.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\Error.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Executor.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\Executor.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Lock.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\Lock.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Processor.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\Processor.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOEventWorker_common.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_common.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOEventWorker_epoll.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_epoll.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOEventWorker_kqueue.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_kqueue.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOEventWorker_stub.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_stub.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOWorker.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOWorker.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/TimerWorker.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker\TimerWorker.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/Worker.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker\Worker.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\async\worker

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/CommandLineArguments.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\base\CommandLineArguments.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\base

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/Countable.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\base\Countable.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\base

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/Environment.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\base\Environment.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\base

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/concurrency/SpinLock.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\concurrency\SpinLock.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\concurrency

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/concurrency/Thread.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\concurrency\Thread.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\concurrency

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/Bundle.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\Bundle.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/buffer/FIFOBuffer.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\buffer\FIFOBuffer.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\buffer

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/buffer/IOBuffer.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\buffer\IOBuffer.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\buffer

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/buffer/Processor.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\buffer\Processor.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\buffer

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/ObjectMapper.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\ObjectMapper.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/TypeResolver.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\TypeResolver.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Any.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Any.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Enum.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Enum.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/List.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\List.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Object.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Object.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/PairList.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\PairList.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Primitive.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Primitive.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Type.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Type.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/UnorderedMap.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedMap.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/UnorderedSet.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedSet.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Vector.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Vector.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/resource/File.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\resource\File.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\resource

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/resource/InMemoryData.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\resource\InMemoryData.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\resource

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/resource/TemporaryFile.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\resource\TemporaryFile.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\resource

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/share/MemoryLabel.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\share\MemoryLabel.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\share

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/share/StringTemplate.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\share\StringTemplate.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\share

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/BufferStream.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream\BufferStream.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/FIFOStream.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream\FIFOStream.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/FileStream.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream\FileStream.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/Stream.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream\Stream.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/StreamBufferedProxy.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream\StreamBufferedProxy.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\data\stream

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/parser/Caret.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\parser\Caret.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\parser

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/parser/ParsingError.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\parser\ParsingError.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\parser

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/Binary.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\utils\Binary.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\utils

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/ConversionUtils.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\utils\ConversionUtils.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\utils

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/Random.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\utils\Random.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\utils

build Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/String.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\core\utils\String.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\core\utils

build Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/encoding/Base64.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\encoding\Base64.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\encoding

build Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/encoding/Hex.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\encoding\Hex.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\encoding

build Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/encoding/Unicode.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\encoding\Unicode.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\encoding

build Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/encoding/Url.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\encoding\Url.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\encoding

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/Address.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\Address.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/ConnectionPool.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\ConnectionPool.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/ConnectionProvider.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\ConnectionProvider.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/ConnectionProviderSwitch.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\ConnectionProviderSwitch.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/Server.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\Server.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/Url.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\Url.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/monitor/ConnectionInactivityChecker.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionInactivityChecker.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\monitor

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMaxAgeChecker.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\monitor

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/monitor/ConnectionMonitor.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMonitor.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\monitor

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/tcp/Connection.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\tcp\Connection.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\tcp

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/tcp/client/ConnectionProvider.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\tcp\client\ConnectionProvider.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\tcp\client

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/tcp/server/ConnectionProvider.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\tcp\server\ConnectionProvider.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\tcp\server

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/Interface.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_\Interface.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/Pipe.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_\Pipe.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/Socket.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_\Socket.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/client/ConnectionProvider.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_\client\ConnectionProvider.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_\client

build Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/server/ConnectionProvider.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_\server\ConnectionProvider.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\network\virtual_\server

build Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/orm/DbClient.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\orm\DbClient.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\orm

build Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/orm/Executor.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\orm\Executor.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\orm

build Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/orm/QueryResult.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\orm\QueryResult.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\orm

build Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/orm/SchemaMigration.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\orm\SchemaMigration.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\orm

build Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/orm/Transaction.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\orm\Transaction.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\orm

build Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/Beautifier.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json\Beautifier.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json

build Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/Utils.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json\Utils.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json

build Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/mapping/Deserializer.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Deserializer.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json\mapping

build Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/mapping/ObjectMapper.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\ObjectMapper.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json\mapping

build Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/mapping/Serializer.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Serializer.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\parser\json\mapping

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/client/ApiClient.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\client\ApiClient.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\client

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/client/HttpRequestExecutor.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\client\HttpRequestExecutor.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\client

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/client/RequestExecutor.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\client\RequestExecutor.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\client

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/client/RetryPolicy.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\client\RetryPolicy.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\client

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/FileProvider.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\FileProvider.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/InMemoryDataProvider.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\InMemoryDataProvider.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/Multipart.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Multipart.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/Part.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Part.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/PartList.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartList.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/PartReader.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartReader.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/Reader.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Reader.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/StatefulParser.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\StatefulParser.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/TemporaryFileProvider.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\TemporaryFileProvider.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\mime\multipart

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/CommunicationError.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\CommunicationError.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/Http.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\Http.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/encoding/Chunked.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\Chunked.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/encoding/ProviderCollection.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\ProviderCollection.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/BodyDecoder.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\BodyDecoder.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/Request.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Request.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\RequestHeadersReader.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/Response.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Response.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\ResponseHeadersReader.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\SimpleBodyDecoder.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/Body.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Body.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/BufferBody.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\BufferBody.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/MultipartBody.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\MultipartBody.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/Request.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Request.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/Response.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Response.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\ResponseFactory.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/StreamingBody.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\StreamingBody.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/utils/CommunicationUtils.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\utils\CommunicationUtils.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\protocol\http\utils

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/AsyncHttpConnectionHandler.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\AsyncHttpConnectionHandler.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/HttpConnectionHandler.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\HttpConnectionHandler.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/HttpProcessor.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\HttpProcessor.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/HttpRouter.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\HttpRouter.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/api/ApiController.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\api\ApiController.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\api

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/api/Endpoint.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\api\Endpoint.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\api

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/handler/AuthorizationHandler.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\handler\AuthorizationHandler.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\handler

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/handler/ErrorHandler.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\handler\ErrorHandler.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\handler

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/interceptor/AllowCorsGlobal.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\interceptor\AllowCorsGlobal.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\server\interceptor

build Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj: CXX_COMPILER__oatpp_unscanned_Debug D$:/MiWebApp/WebServerApp/Oat++/oatpp/web/url/mapping/Pattern.cpp || cmake_object_order_depends_target_oatpp
  CONFIG = Debug
  DEP_FILE = Oat++\CMakeFiles\oatpp.dir\oatpp\web\url\mapping\Pattern.cpp.obj.d
  FLAGS = -g -std=c++11
  INCLUDES = -ID:/MiWebApp/WebServerApp/Oat++
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  OBJECT_FILE_DIR = Oat++\CMakeFiles\oatpp.dir\oatpp\web\url\mapping


# =============================================================================
# Link build statements for STATIC_LIBRARY target oatpp


#############################################
# Link the static library Oat++\liboatpp.a

build Oat++/liboatpp.a: CXX_STATIC_LIBRARY_LINKER__oatpp_Debug Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj
  CONFIG = Debug
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = Oat++\CMakeFiles\oatpp.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = Oat++\liboatpp.a
  TARGET_PDB = oatpp.a.dbg
  RSP_FILE = CMakeFiles\oatpp.rsp


#############################################
# Utility command for edit_cache

build Oat++/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\MiWebApp\WebServerApp\out\build\AMD\Oat++ && "C:\Program Files\CMake\bin\cmake-gui.exe" -SD:\MiWebApp\WebServerApp -BD:\MiWebApp\WebServerApp\out\build\AMD"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build Oat++/edit_cache: phony Oat++/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build Oat++/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D D:\MiWebApp\WebServerApp\out\build\AMD\Oat++ && "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SD:\MiWebApp\WebServerApp -BD:\MiWebApp\WebServerApp\out\build\AMD"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build Oat++/rebuild_cache: phony Oat++/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build MiWebServer: phony MiWebServer.exe

build liboatpp.a: phony Oat++/liboatpp.a

build oatpp: phony Oat++/liboatpp.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/MiWebApp/WebServerApp/out/build/AMD

build codegen: phony Oat++/codegen

# =============================================================================

#############################################
# Folder: D:/MiWebApp/WebServerApp/out/build/AMD/Oat++

build Oat++/codegen: phony

# =============================================================================

#############################################
# Folder: D:/MiWebApp/WebServerApp/out/build/AMD

build all: phony MiWebServer.exe Oat++/all

# =============================================================================

#############################################
# Folder: D:/MiWebApp/WebServerApp/out/build/AMD/Oat++

build Oat++/all: phony Oat++/liboatpp.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja D$:/MiWebApp/WebServerApp/out/build/AMD/cmake_install.cmake D$:/MiWebApp/WebServerApp/out/build/AMD/Oat++/cmake_install.cmake: RERUN_CMAKE | C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeRCInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CheckIncludeFile.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Compiler/GNU-C.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Compiler/GNU-CXX.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Compiler/GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/FindPackageMessage.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Linker/GNU-C.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Linker/GNU-CXX.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Linker/GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Linker/GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-C.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-CXX.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU-C-ABI.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU-C.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX-ABI.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-windres.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake CMakeCache.txt CMakeFiles/4.0.3/CMakeCCompiler.cmake CMakeFiles/4.0.3/CMakeCXXCompiler.cmake CMakeFiles/4.0.3/CMakeRCCompiler.cmake CMakeFiles/4.0.3/CMakeSystem.cmake D$:/MiWebApp/WebServerApp/CMakeLists.txt D$:/MiWebApp/WebServerApp/Oat++/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeCInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeCXXInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeGenericSystem.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeRCInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CheckIncludeFile.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Compiler/GNU-C.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Compiler/GNU-CXX.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Compiler/GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/FindPackageMessage.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Linker/GNU-C.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Linker/GNU-CXX.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Linker/GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Linker/GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-C.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-CXX.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU-C-ABI.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU-C.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX-ABI.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-GNU.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows-windres.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/Windows.cmake C$:/Program$ Files/CMake/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake CMakeCache.txt CMakeFiles/4.0.3/CMakeCCompiler.cmake CMakeFiles/4.0.3/CMakeCXXCompiler.cmake CMakeFiles/4.0.3/CMakeRCCompiler.cmake CMakeFiles/4.0.3/CMakeSystem.cmake D$:/MiWebApp/WebServerApp/CMakeLists.txt D$:/MiWebApp/WebServerApp/Oat++/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
